import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer-core';
import chromium from '@sparticuz/chromium';
import { InvoiceData } from '../../store/invoiceStore';

declare global {
  interface Window {
    __INVOICE_DATA__: InvoiceData;
  }
}

/**
 * Verifica se il server è raggiungibile
 */
async function checkServerHealth(url: string, maxRetries = 5): Promise<boolean> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        console.log(`[generatePdf] Server health check passed for ${url}`);
        return true;
      }
    } catch (error) {
      console.log(`[generatePdf] Server health check attempt ${i + 1}/${maxRetries} failed:`, error);
    }

    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Aspetta 1 secondo
    }
  }
  return false;
}

/**
 * Genera un PDF da una pagina Next.js (route /print) usando Puppeteer.
 * @param data Dati della fattura
 * @param layout Layout della fattura (classic, modern, minimal)
 * @returns Buffer PDF
 */
export async function generatePdf(data: InvoiceData, layout: string): Promise<Uint8Array> {
  // In prod, puoi usare @sparticuz/chromium (Vercel/AWS) oppure path custom
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const url = `${baseUrl}/print?layout=${layout}`;

  console.log(`[generatePdf] Attempting to generate PDF from URL: ${url}`);

  // Verifica che il server sia raggiungibile prima di lanciare Puppeteer
  const isServerReady = await checkServerHealth(baseUrl);
  if (!isServerReady) {
    throw new Error(`Server non raggiungibile all'indirizzo ${baseUrl}`);
  }

  const isProd = process.env.NODE_ENV === 'production';
  const executablePath = isProd
    ? await chromium.executablePath()
    : process.env.CHROME_EXECUTABLE_PATH;

  let browser: Browser | null = null;
  try {
    console.log('[generatePdf] Launching browser...');
    browser = await puppeteer.launch({
      args: isProd ? chromium.args : ['--no-sandbox', '--disable-setuid-sandbox'],
      executablePath: executablePath as string,
      headless: isProd ? chromium.headless : true,
    });

    const page = await browser.newPage();

    // Inietta i dati lato client prima del caricamento della pagina
    console.log('[generatePdf] Injecting invoice data...');
    await page.evaluateOnNewDocument((invoiceData: InvoiceData) => {
      window.__INVOICE_DATA__ = invoiceData;
    }, data);

    console.log(`[generatePdf] Navigating to ${url}...`);

    // Retry mechanism per la navigazione
    let navigationSuccess = false;
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        await page.goto(url, {
          waitUntil: 'networkidle0',
          timeout: 30000 // 30 secondi di timeout
        });
        navigationSuccess = true;
        console.log(`[generatePdf] Navigation successful on attempt ${attempt}`);
        break;
      } catch (navError) {
        console.error(`[generatePdf] Navigation attempt ${attempt} failed:`, navError);
        if (attempt === 3) {
          throw navError;
        }
        // Aspetta un po' prima del prossimo tentativo
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    if (!navigationSuccess) {
      throw new Error('Failed to navigate to print page after 3 attempts');
    }

    console.log('[generatePdf] Waiting for content to load...');
    // Attendi che il client component sia montato
    await page.waitForSelector('.bg-white', { timeout: 15000 });

    console.log('[generatePdf] Generating PDF...');
    const pdfBuffer: Uint8Array = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      }
    });

    console.log('[generatePdf] PDF generated successfully');
    return pdfBuffer;
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error(String(e));
    console.error('[generatePdf] PDF generation failed', error);
    throw new Error(error.message);
  } finally {
    if (browser) {
      try {
        console.log('[generatePdf] Closing browser...');
        await browser.close();
      } catch (closeErr) {
        console.error('[generatePdf] Failed to close browser', closeErr);
      }
    }
  }
}
